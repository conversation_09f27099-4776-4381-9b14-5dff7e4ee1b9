from dataclasses import dataclass
import datetime as dt
import logging
from typing import Any, Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from constants.extracted_data import MissingDataStatus
from constants.extracted_data import ConversationState
from constants.message import (
    BRIEF_DESCRIPTION_REPLY,
    CLIENT_NAME_CONFIRMED,
    EXAMPLE_REPLY,
    UNDEFINED_REPLY,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    OptionType,
)
from exceptions import EntityNotFoundError
from repositories import ConversationMessageRepository, ConversationRepository
from schemas import (
    BaseMessageSerializer,
    ClientNameOption,
    CombinedMessageSerializer,
    ConversationMessageIntentClassifierServiceResponse,
    ConversationMessageProcessingResult,
    DatePickerOption,
    DocumentCreationRequest,
    LDMFCountryOption,
    MessageValidator,
    Option,
    SystemMessageSerializer,
    UserMessageSerializer,
)

from .document import DocumentService
from .extracted_data import ExtractedDataService
from .intent_classifier import IntentClassifierService
from .kx_dash import KXDashService


__all__ = ['ConversationMessageService']

logger = logging.getLogger(__name__)


class ConversationMessageService:
    """Service for conversation message-related business logic."""

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        conversation_repository: ConversationRepository,
        document_service: DocumentService,
        kx_dash_service: KXDashService,
        intent_classifier_service: IntentClassifierService,
        extracted_data_service: ExtractedDataService,
    ):
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository
        self.document_service = document_service
        self.kx_dash_service = kx_dash_service
        self.intent_classifier_service = intent_classifier_service
        self.extracted_data_service = extracted_data_service

    async def create(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
    ) -> CombinedMessageSerializer:
        """
        Create a new conversation message with attached files.

        Args:
            conversation_id: UUID of the conversation
            content: Text content of the message
            selected_option: The selected option
            files: Optional list of files to attach

        Returns:
            Response containing both user message and system message with expected entity

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            MaximumDocumentsNumberExceeded: If too many documents are attached
            MaximumDocumentsSizeExceeded: If documents exceed size limit
            ValueError: If file validation fails
        """
        content = content.strip()
        if files:
            message_type = MessageType.TEXT_WITH_FILE if content else MessageType.FILE
        else:
            message_type = MessageType.TEXT

        user_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=message_type,
            content=content,
            selected_option=selected_option,
            files=files,
        )

        if selected_option:
            match selected_option.type:
                case OptionType.CLIENT_NAME:
                    system_message = await self._handle_client_name_selection(selected_option, conversation_id)
                case OptionType.LDMF_COUNTRY:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
                case OptionType.DATES:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
                case OptionType.KX_DASH_TASK:
                    system_message = await self.kx_dash_service.on_select(selected_option, conversation_id)
                case _:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
        else:
            if content:
                message_processor = ConversationMessageProcessor(
                    conversation_id=conversation_id,
                    user_message=content,
                    files=files,
                    intent_classifier_service=self.intent_classifier_service,
                    extracted_data_service=self.extracted_data_service,
                    conversation_repository=self.conversation_repository,
                )
                message_processing_result: ConversationMessageProcessingResult = await message_processor.run()
                user_message.intention = message_processing_result.intention
                system_reply = message_processing_result.system_reply
            else:
                system_reply = ''
                message_processing_result = ConversationMessageProcessingResult(
                    intention=ConversationMessageIntention.UNDEFINED,
                    system_reply=system_reply,
                    data={}
                )

            # TODO: Remove this once we have a real system message
            system_message = self._get_system_message_with_result(user_message, message_processing_result)

        response = CombinedMessageSerializer(
            user=cast(UserMessageSerializer, await self.create_message(user_message)),
            system=cast(SystemMessageSerializer, await self.create_message(system_message)),
        )

        if user_message.intention == ConversationMessageIntention.EXTRACTION:
            await self.document_service.create_prompt(
                content=user_message.content, signalr_user_id=conversation_id, message_id=response.user.id
            )

        if files:
            document_data = DocumentCreationRequest(
                conversation_id=user_message.conversation_id,
                files=files,
                message_id=response.user.id,
            )
            response.files = await self.document_service.create_many(document_data)

        return response

    async def create_message(self, message_data: MessageValidator) -> BaseMessageSerializer:
        """
        Create a new conversation message.

        Args:
            message_data: Data for creating the message

        Returns:
            Response with the created message data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error creating the message
        """
        logger.debug('Creating new message for conversation ID: %s', message_data.conversation_id)
        try:
            return await self.conversation_message_repository.create(message_data)

        except Exception as e:
            logger.error('Error creating message: %s', e)
            raise

    async def get(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get a message by its ID.

        Args:
            public_id: The ID of the message

        Returns:
            The message response

        Raises:
            EntityNotFoundError: If the message doesn't exist
            DatabaseException: If there's an error retrieving the message
        """
        try:
            logger.debug('Retrieving message with ID: %s', public_id)
            return await self.conversation_message_repository.get(public_id)

        except Exception as e:
            logger.error('Error retrieving message: %s', e)
            raise

    async def list(self, public_id: UUID) -> Sequence[BaseMessageSerializer]:
        """
        Get all messages for a specific conversation.

        Args:
            public_id: The ID of the conversation

        Returns:
            Sequence of messages for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the messages
        """
        if not await self.conversation_repository.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        try:
            logger.debug('Retrieving all messages for conversation with ID: %s', public_id)
            return await self.conversation_message_repository.list(public_id)

        except Exception as e:
            logger.error('Error retrieving messages for conversation %s: %s', public_id, e)
            raise

    async def get_last(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get the last message for a specific conversation.

        Args:
            public_id: The ID of the conversation

        Returns:
            The last message for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the last message
        """
        try:
            logger.debug('Retrieving last message for conversation with ID: %s', public_id)
            return await self.conversation_message_repository.get_last(public_id)

        except Exception as e:
            logger.error('Error retrieving last message for conversation %s: %s', public_id, e)
            raise

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all messages for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            None

        Raises:
            DatabaseException: If there's an error deleting the messages
        """
        try:
            logger.debug('Deleting all messages for conversation with ID: %s', conversation_id)
            await self.conversation_message_repository.delete_many(conversation_id)
        except Exception as e:
            logger.error('Error deleting messages for conversation %s: %s', conversation_id, e)
            raise

    def _get_system_message_with_mocked_result(
        self,
        message_data: MessageValidator,
        system_reply: str,
    ) -> MessageValidator:
        """
        Create a system message with an expected entity based on the message content.
        """
        # Determine expected entity based on content
        content = message_data.content.lower()
        if 'date' in content:
            options = [
                DatePickerOption(
                    start_date=dt.date.fromisoformat('2025-05-01'), end_date=dt.date.fromisoformat('2025-07-01')
                ),
            ]
        elif 'country' in content:
            options = [
                LDMFCountryOption(ldmf_country='Austria'),
                LDMFCountryOption(ldmf_country='France'),
            ]
        elif 'client' in content:
            options = [
                ClientNameOption(client_name='FashionForward'),
                ClientNameOption(client_name='FashionForward GMBH'),
            ]
        else:
            options = []

        return MessageValidator(
            conversation_id=message_data.conversation_id,
            role=MessageRole.SYSTEM,
            type=message_data.type,
            content=system_reply,
            options=options,
        )

    async def get_owner_id(self, message_id: UUID) -> UUID | None:
        """
        Get an owner ID for the message.

        Args:
            message_id: The ID of the message

        Returns:
            The user ID if the message exists, None otherwise
        """
        try:
            logger.debug('Retrieving an owner ID for the message: %s', message_id)
            return await self.conversation_message_repository.get_owner_id(message_id)

        except Exception:
            logger.exception('Failed to retrieve message owner ID')
            raise

    async def _handle_client_name_selection(self, selected_option: ClientNameOption, conversation_id: UUID) -> MessageValidator:
        """
        Handle client name selection from options.

        Args:
            selected_option: The selected client name option
            conversation_id: The conversation ID

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug('Handling client name selection: %s for conversation: %s', selected_option.client_name, conversation_id)

            # Update confirmed data with the selected client name
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name='client_name',
                field_value=selected_option.client_name,
                state=ConversationState.COLLECTING_COUNTRY  # Move to next state
            )

            # Create confirmation message
            confirmation_message = CLIENT_NAME_CONFIRMED.format(client_name=selected_option.client_name)

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
            )

        except Exception as e:
            logger.error('Error handling client name selection: %s', e)
            raise


@dataclass(frozen=True)
class ConversationMessageProcessor:
    """Processor for conversation message intention."""

    conversation_id: UUID
    user_message: str
    files: list[UploadFile] | None
    intent_classifier_service: IntentClassifierService
    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository

    async def _get_intent(self) -> ConversationMessageIntention:
        """Intent getter."""
        intent_classification_response: ConversationMessageIntentClassifierServiceResponse = (
            await self.intent_classifier_service.classify_intent(
                user_message=self.user_message,
                response_cls=ConversationMessageIntentClassifierServiceResponse,
            )
        )
        intention = intent_classification_response.intention
        logger.info(f'Classified intention: {intention}')
        return intention

    async def run(self) -> ConversationMessageProcessingResult:
        """Process the intention."""

        if not self.user_message:
            raise ValueError('You cannot classify intent with empty user message.')

        intention = await self._get_intent()

        if intention == ConversationMessageIntention.UNDEFINED:
            data = self._process_undefined()

        elif intention == ConversationMessageIntention.GENERATE_QUAL:
            data = self._generate_qual()
        elif intention == ConversationMessageIntention.EXTRACTION:
            data = self._extract_data()
        elif intention == ConversationMessageIntention.EXAMPLE:
            data = self._example_help()
        elif intention == ConversationMessageIntention.DASH_DISCARD:
            data = self._dash_discard()
        elif intention == ConversationMessageIntention.UNCERTAINITY:
            data = await self._uncertainty()
        elif intention == ConversationMessageIntention.NEED_CONTEXT:
            data = await self._uncertainty()
        else:
            raise NotImplementedError(f'Intent {intention} not implemented')

        system_reply = data.pop('system_reply')
        return ConversationMessageProcessingResult(
            intention=intention,
            system_reply=system_reply,
            data=data,
        )

    def _process_undefined(self) -> dict[str, Any]:
        return {'system_reply': UNDEFINED_REPLY}

    def _generate_qual(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to generate a qual
        # ...
        system_reply = f'Finished processing `_generate_qual` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _extract_data(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to extract data
        # ...
        system_reply = f'Finished processing `_extract_data` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _dash_show_more(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to show more dash tasks
        # ...
        system_reply = f'Finished processing `_dash_show_more` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _dash_discard(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to discard dash tasks
        # ...
        system_reply = f'Finished processing `_dash_discard` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _example_help(self) -> dict[str, Any]:
        return {'system_reply': EXAMPLE_REPLY}

    async def _uncertainty(self) -> dict[str, Any]:
        """
        Handle uncertainty intention with progressive data collection.

        This method triggers the missing data collection flow, checking what
        information is still needed and guiding the user through confirmation.
        """
        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise ValueError(f'Conversation {self.conversation_id} not found')

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)

            # Check if user is providing manual input for client name
            if (conversation.State == ConversationState.COLLECTING_CLIENT_NAME.value and
                confirmed_data.client_name is None and
                self.user_message.strip()):

                # Extract client name from user message and save it
                client_name = self.user_message.strip()
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name='client_name',
                    field_value=client_name,
                    state=ConversationState.COLLECTING_COUNTRY
                )

                confirmation_message = CLIENT_NAME_CONFIRMED.format(client_name=client_name)
                return {
                    'system_reply': confirmation_message,
                    'missing_data_status': MissingDataStatus.MISSING_DATA,
                }

            # Check what data is missing using the enhanced service
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=self.conversation_id, confirmed_data=confirmed_data
            )

            # Update conversation state based on the response
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )

                return {
                    'system_reply': missing_data_response.message,
                    'missing_data_status': missing_data_response.status,
                    'next_expected_field': missing_data_response.next_expected_field,
                    'missing_fields': missing_data_response.missing_fields,
                    'options': missing_data_response.options,
                    'conversation_state': missing_data_response.conversation_state,
                }

            elif missing_data_response.status == MissingDataStatus.DATA_COMPLETE:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )

                return {
                    # 'system_reply': 'Great! I have all the information needed to create your qual. Would you like me to generate it now?',
                    'system_reply': BRIEF_DESCRIPTION_REPLY,  # Fallback to original behavior
                    'missing_data_status': missing_data_response.status,
                }

            else:  # error status
                logger.error(
                    'Error in missing data collection for conversation %s: %s',
                    self.conversation_id,
                    missing_data_response.message,
                )
                return {
                    'system_reply': BRIEF_DESCRIPTION_REPLY,  # Fallback to original behavior
                    'missing_data_status': MissingDataStatus.ERROR,
                }

        except Exception as e:
            logger.error('Exception in _uncertainty method for conversation %s: %s', self.conversation_id, e)
            # Fallback to original behavior on any error
            return {'system_reply': BRIEF_DESCRIPTION_REPLY}
