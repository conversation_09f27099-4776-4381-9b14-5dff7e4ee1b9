import asyncio
from abc import ABC, abstractmethod

from constants.extracted_data import <PERSON><PERSON><PERSON><PERSON>
from constants.message import CLIENT_NAME_MULTIPLE_OPTIONS, NEED_INFO_OUTCOMES
from repositories import QualsClientsRepository
from schemas import AggregatedData, ClientSearchRequest, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData


__all__ = [
    'ClientNameHandler',
    'LDMFCountryHandler',
    'DateIntervalsHandler',
    'ObjectiveHandler',
    'OutcomesHandler',
]


class BaseFieldHandler(ABC):
    """
    Abstract base class for handling missing required fields and generating prompts.
    """

    @abstractmethod
    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, quals_clients_repository: QualsClientsRepository | None = None
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
            quals_clients_repository: Optional repository for client API calls.

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        pass


class ClientNameHandler(BaseFieldHandler):
    """Handler for RequiredField.CLIENT_INFO."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, quals_clients_repository: QualsClientsRepository | None = None
    ) -> FieldHandlerResponse:
        # Check if client name is already confirmed
        if confirmed_data.client_name is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )

        # Check if we have multiple client names in aggregated data
        if len(aggregated_data.client_name) > 1:
            # Search for each client name using the API
            if quals_clients_repository:
                try:
                    # Create search requests for each client name
                    search_requests = [
                        ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
                        for client_name in aggregated_data.client_name
                    ]

                    # Perform concurrent searches
                    search_results = await asyncio.gather(
                        *[quals_clients_repository.search_clients(request) for request in search_requests],
                        return_exceptions=True
                    )

                    # Collect found client names
                    found_client_names = []
                    for i, result in enumerate(search_results):
                        if not isinstance(result, Exception) and hasattr(result, 'clients') and result.clients:
                            # Use the original client name from aggregated data if API found results
                            found_client_names.append(aggregated_data.client_name[i])

                    if found_client_names:
                        return FieldHandlerResponse(
                            needs_confirmation=True,
                            field_status=FieldStatus.PENDING_CONFIRMATION,
                            system_message=CLIENT_NAME_MULTIPLE_OPTIONS,
                            next_expected_field=None,
                            options=found_client_names,
                        )
                except Exception:
                    # If API search fails, fall back to showing all options
                    pass

            # Fallback: show all client names as options
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MULTIPLE,
                system_message=CLIENT_NAME_MULTIPLE_OPTIONS,
                next_expected_field=None,
                options=aggregated_data.client_name,
            )

        # Check if we have a single client name
        elif len(aggregated_data.client_name) == 1:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=f'I found this client name: "{aggregated_data.client_name[0]}". Can you confirm this is correct?',
                next_expected_field=None,
                options=aggregated_data.client_name,
            )

        # No client names found
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message='I need the client name to create the qual. Can you provide the client name?',
                next_expected_field=None,
            )


class LDMFCountryHandler(BaseFieldHandler):
    """Handler for RequiredField.LDMF_COUNTRY."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, quals_clients_repository: QualsClientsRepository | None = None
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.MISSING,
            system_message='Implement ldmf handler',
            next_expected_field=None,
        )


class DateIntervalsHandler(BaseFieldHandler):
    """Handler for RequiredField.ENGAGEMENT_DATES."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, quals_clients_repository: QualsClientsRepository | None = None
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.MISSING,
            system_message='Implement date intervals handler',
            next_expected_field=None,
        )


class ObjectiveHandler(BaseFieldHandler):
    """Handler for RequiredField.OBJECTIVE_SCOPE."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, quals_clients_repository: QualsClientsRepository | None = None
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.MISSING,
            system_message='Implement objective and scope handler',
            next_expected_field=None,
        )


class OutcomesHandler(BaseFieldHandler):
    """Handler for RequiredField.OUTCOMES."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, quals_clients_repository: QualsClientsRepository | None = None
    ) -> FieldHandlerResponse:
        # Check if outcomes are confirmed
        if confirmed_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if outcomes are in aggregated data
        elif aggregated_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=f'found this outcomes: {aggregated_data.outcomes} can you confirm?',
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_OUTCOMES,
                next_expected_field=None,
            )
